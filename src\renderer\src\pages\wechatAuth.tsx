import { useEffect, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { useWechatAuth, useWechatBindPhone } from '@renderer/hooks/useWechatLogin'
import { useGlobalStorageService } from '@renderer/infrastructure/services'
import { useNotify } from '@renderer/hooks/use-notify'
import { Loader2 } from 'lucide-react'
import { Card, CardContent, CardHeader } from '@renderer/shadcn-components/ui/card'
import { LinkAccountForm } from '@renderer/components/LinkAccountForm'
import { Button } from '@renderer/shadcn-components/ui/button'

export function WechatAuth() {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const wechatAuthMutation = useWechatAuth()
  const globalStorageService = useGlobalStorageService()
  const { notifyService } = useNotify()
  const [newUser, setNewUser] = useState(false)
  const [showLinkAccount, setShowLinkAccount] = useState(false)

  // 关联微信账号
  const linkAccountMutation = useWechatBindPhone()

  const code = searchParams.get('code')
  const state = searchParams.get('state')

  useEffect(() => {
    if (!code || !state) {
      notifyService.error('微信登录参数缺失')
      navigate('/login')
      return
    }

    wechatAuthMutation.mutate(
      { code, state },
      {
        onSuccess: (data) => {
          globalStorageService.setToken(data.authorization)
          notifyService.success('微信登录成功！')
          if (data.newUser) {
            setNewUser(true)
          } else {
            setNewUser(false)
            navigate('/')
          }
        },
        onError: () => {
          navigate('/login')
        },
      },
    )
  }, [searchParams, globalStorageService, navigate])

  const onCreateNewAccount = () => {
    // 调用关联账号的API
    linkAccountMutation.mutate(
      {
        authorization: globalStorageService.getToken() || '',
      },
      {
        onSuccess: (data) => {
          globalStorageService.setToken(data.authorization)
          navigate('/')
        },
      },
    )
  }

  return (
    <div className="flex h-full items-center justify-center bg-gray-50 bg-[url('@renderer/assets/passport/background.png')] bg-cover">
      {wechatAuthMutation.isPending && (
        <div className="text-center">
          <Loader2 className="mx-auto h-8 w-8 animate-spin text-green-600" />
          <p className="mt-4 text-gray-600">正在处理微信登录...</p>
        </div>
      )}

      {newUser && !showLinkAccount && (
        <Card className="relative h-[534px] w-[500px] rounded-[20px] border-0 bg-white/95 shadow-xl backdrop-blur-sm">
          <CardHeader className="pb-[54px] pt-[130px]">
            <div className="text-center">
              <h2 className="text-2xl font-semibold text-gray-900">首次扫码</h2>
              <h2 className="mt-3 text-sm text-[#666666]">请确认是否需要创建新账号?</h2>
            </div>
          </CardHeader>

          <CardContent className="px-[70px] pb-[100px]">
            <Button
              className="h-[54px] w-full bg-primary py-2.5 font-medium text-white hover:bg-primary/90"
              onClick={() => setShowLinkAccount(true)}
            >
              关联现有账号
            </Button>

            <Button
              variant={'outline'}
              className="mt-4 h-[54px] w-full py-2.5 font-medium"
              onClick={() => onCreateNewAccount()}
            >
              创建新账号
            </Button>
          </CardContent>
        </Card>
      )}

      {newUser && showLinkAccount && (
        <LinkAccountForm code={code || undefined} state={state || undefined} />
      )}
    </div>
  )
}
