import { useUserApiService } from '@renderer/infrastructure/services/network-service'
import { useCallback, useMemo } from 'react'
import type {
  CollGroupsDTO,
  CollGroupsList,
} from '@renderer/infrastructure/services/application-service/sit-group-service'
import { FavoriteGroupOption } from '@renderer/pages/IndexPage/ShortcutSettingDialog'

export function useFavoriteGroupApi() {
  const userApiService = useUserApiService()

  const deleteFavoriteGroup = useCallback(
    (groupId: string) => {
      return userApiService.delete(`/collect-groups/${groupId}`)
    },
    [userApiService],
  )

  const updateFavoriteGroup = useCallback(
    (groupId: string, name?: string, browsers?: string[]) => {
      return userApiService.patch(`/collect-groups/${groupId}`, { name, browsers })
    },
    [userApiService],
  )

  const addFavoriteGroup = useCallback(
    (name: string) => {
      return userApiService.post('/collect-groups', { name })
    },
    [userApiService],
  )

  const setFavoriteGroup = useCallback(
    (
      groupId: string,
      values: { url: string; spaceId: string; accountId: string; originalId: string }[],
    ) => {
      return userApiService.patch(
        `/collect-groups/${groupId}/set_favorites`,
        values.map((item) => ({
          accountId: item.accountId,
          browserId: item.spaceId,
          originalId: item.originalId,
          websiteUrl: item.url,
        })),
      )
    },
    [userApiService],
  )

  const getFavoriteGroups = useCallback(
    ({ page, name }: { page: number; name?: string }) => {
      return userApiService.get('/collect-groups', {
        page: page.toString(),
        size: '20',
        name,
      }) as Promise<CollGroupsList>
    },
    [userApiService],
  )

  const getFavoriteGroupOptions = useCallback(async () => {
    const response = await userApiService.get<CollGroupsList>('/collect-groups', {
      page: '1',
      size: '99999',
    })
    return response.data.map((group) => new FavoriteGroupOption(group.id, group.name))
  }, [userApiService])

  const getFavoriteGroup = useCallback(
    (groupId: string) => {
      return userApiService.get<CollGroupsDTO>(`/collect-groups/${groupId}`)
    },
    [userApiService],
  )

  return useMemo(
    () => ({
      deleteFavoriteGroup,
      updateFavoriteGroup,
      addFavoriteGroup,
      setFavoriteGroup,
      getFavoriteGroups,
      getFavoriteGroup,
      getFavoriteGroupOptions,
    }),
    [
      addFavoriteGroup,
      deleteFavoriteGroup,
      getFavoriteGroups,
      setFavoriteGroup,
      updateFavoriteGroup,
      getFavoriteGroup,
      getFavoriteGroupOptions,
    ],
  )
}
