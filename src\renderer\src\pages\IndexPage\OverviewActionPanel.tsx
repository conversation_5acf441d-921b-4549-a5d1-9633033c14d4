import IconPublishVideo from '@renderer/assets/index-page/feature/icon-publish-video.svg'
import IconPublishImageText from '@renderer/assets/index-page/feature/icon-publish-image-text.svg'
import IconPublishArticle from '@renderer/assets/index-page/feature/icon-publish-article.svg'
import IconPublishGongZhongHao from '@renderer/assets/index-page/feature/icon-publish-gong-zhong-hao.svg'
import IconAddAccount from '@renderer/assets/index-page/feature/icon-add-account.svg'
import IconNotLoggedAccount from '@renderer/assets/index-page/feature/icon-not-logged-account.svg'
import DecorationPublishVideo from '@renderer/assets/index-page/feature/decoration-publish-video.png'
import DecorationPublishImageText from '@renderer/assets/index-page/feature/decoration-publish-image-text.png'
import DecorationPublishArticle from '@renderer/assets/index-page/feature/decoration-publish-article.png'
import DecorationPublishGongZhongHao from '@renderer/assets/index-page/feature/decoration-publish-gong-zhong-hao.png'
import { useDialogStateStore } from '@renderer/store/dialogStateStore'
import { usePushLimits } from '@renderer/hooks/application/use-push-limits'
import { useFeatureManager } from '@renderer/infrastructure/services'
import { features } from '@renderer/infrastructure/model'
import { FavoriteGroups } from './FavoriteGroups'
import { useAccountCheckService } from '@renderer/infrastructure/services/application-service/accountCheck-service'
import { useSystem } from '@renderer/pages/context'
import { useEffect, useState } from 'react'
import type React from 'react'

export function OverviewActionPanel() {
  const { checkPushLimitation } = usePushLimits()
  const { openFeature } = useFeatureManager()
  const setAddAccountOpen = useDialogStateStore((store) => store.setAddAccountOpen)
  const accountCheckService = useAccountCheckService()
  const { onSetDialog } = useSystem()
  const [failedAccountCount, setFailedAccountCount] = useState(0)

  useEffect(() => {
    const fetchFailedAccountCount = async () => {
      try {
        const failedAccounts = await accountCheckService.getAccountsByFail()
        setFailedAccountCount(failedAccounts.length)
      } catch (error) {
        console.error('获取失效账号数量失败:', error)
        setFailedAccountCount(0)
      }
    }

    fetchFailedAccountCount()
  }, [accountCheckService])

  return (
    <div className="grid grid-cols-4 gap-[12px] rounded-lg bg-white p-4">
      <LargeCard
        icon={IconPublishVideo}
        title="发视频"
        decorationImage={DecorationPublishVideo}
        gradientFrom="#F3F2FFB3"
        gradientTo="#F3F2FFFF"
        onClick={async () => {
          await checkPushLimitation()
          openFeature(features.发布视频)
        }}
      />

      <LargeCard
        icon={IconPublishImageText}
        title="发图文"
        decorationImage={DecorationPublishImageText}
        gradientFrom="#EDF6FFB3"
        gradientTo="#EDF6FFFF"
        onClick={async () => {
          await checkPushLimitation()
          openFeature(features.发布图文)
        }}
      />

      <LargeCard
        icon={IconPublishArticle}
        title="发文章"
        decorationImage={DecorationPublishArticle}
        gradientFrom="#FFF2F2B3"
        gradientTo="#FFF2F2FF"
        onClick={async () => {
          await checkPushLimitation()
          openFeature(features.发布文章)
        }}
      />

      <LargeCard
        icon={IconPublishGongZhongHao}
        title="发公众号"
        decorationImage={DecorationPublishGongZhongHao}
        gradientFrom="#F3F2FFB3"
        gradientTo="#F3F2FFFF"
        onClick={async () => {
          await checkPushLimitation()
          openFeature(features.发布公众号)
        }}
      />

      <div className="flex flex-col gap-3">
        <SmallCard
          icon={IconAddAccount}
          title="添加账号"
          onClick={() => {
            setAddAccountOpen(true)
          }}
        />

        <SmallCard
          icon={IconNotLoggedAccount}
          title={
            failedAccountCount > 0 ? (
              <>
                失效账号<span className="pl-1 text-red-500">{failedAccountCount}</span>
              </>
            ) : (
              '失效账号'
            )
          }
          onClick={() => {
            onSetDialog((dialogMap) => ({
              ...dialogMap.accountStateLogin,
            }))
          }}
        />
      </div>

      <FavoriteGroups />
    </div>
  )
}

interface LargeCardProps {
  /** 图标 */
  icon: string
  /** 标题文本 */
  title: string
  /** 装饰图片 */
  decorationImage: string
  /** 渐变色起始颜色 */
  gradientFrom: string
  /** 渐变色结束颜色 */
  gradientTo: string
  /** 点击事件处理函数 */
  onClick: () => void | Promise<void>
}

export function LargeCard({
  icon,
  title,
  decorationImage,
  gradientFrom,
  gradientTo,
  onClick,
}: LargeCardProps) {
  return (
    <div
      style={{
        background: `linear-gradient(to right, ${gradientFrom}, ${gradientTo})`,
      }}
      onClick={onClick}
      className="relative flex h-[124px] flex-1 cursor-pointer flex-col justify-center rounded-lg"
    >
      <div className="flex flex-col gap-2 px-6">
        <img src={icon} alt="" className="h-8 w-8" />
        <span className="text-sm font-semibold">{title}</span>
      </div>
      <img src={decorationImage} alt="" className="absolute right-0 top-0 h-[80px] w-[80px]" />
    </div>
  )
}

interface SmallCardProps {
  /** 图标 */
  icon: string
  /** 标题文本或React节点 */
  title: string | React.ReactNode
  /** 点击事件处理函数 */
  onClick: () => void | Promise<void>
}

export function SmallCard({ icon, title, onClick }: SmallCardProps) {
  return (
    <div
      className="flex cursor-pointer items-center gap-3 rounded-lg px-6 py-3"
      style={{
        background:
          'linear-gradient(to right, rgba(233, 234, 252, 0.3), rgba(233, 234, 252, 0.85))',
      }}
      onClick={onClick}
    >
      <img src={icon} alt="" className="h-8 w-8" />
      <span className="text-sm font-semibold">{title}</span>
    </div>
  )
}
