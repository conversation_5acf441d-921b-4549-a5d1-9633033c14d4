import { useMemo, useRef, useState } from 'react'
import { keepPreviousData } from '@tanstack/react-query'
import { Loading } from '@renderer/components/LoadingContainer'
import type { PaginationState } from '@tanstack/react-table'
import { getTableColumns } from './columns'
import { alertBaseManager } from '@renderer/components/alertBase'
import { useVipDialog } from '../Vip/vipDialogProvider'
import type { WarpPage } from '@renderer/hooks/useApiQuery'
import { useApiMutation, useApiQuery } from '@renderer/hooks/useApiQuery'
import type { Order } from '../Vip/types/vip'
import OrderDetail from './detail'
import { useContextStore } from '@renderer/store/contextStore'
import { StickyDataTable } from '@renderer/components/table/sticky-data-table'

// 团队订单核心组件
export function TeamOrderContent() {
  const activeId = useRef('')
  const [detailOpen, setDetailOpen] = useState(false)
  const { pay } = useVipDialog()
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  })
  const serviceQrCode = useContextStore((s) => s.currentTeam)?.corporateTransfer

  const vipOrdersQuery = useApiQuery<WarpPage<Order>>(
    {
      url: '/orders',
      method: 'GET',
      params: {
        size: pagination.pageSize,
        page: pagination.pageIndex + 1,
      },
    },
    ['vipOrders', pagination],
    {
      placeholderData: keepPreviousData,
    },
  )

  const refetch = vipOrdersQuery.refetch

  const statusMutation = useApiMutation<string, void>(
    (orderNo) => ({
      url: `/orders/${orderNo}/status`,
      method: 'PUT',
      data: {
        orderStatus: 'cancelled',
      },
    }),
    {
      onSuccess: () => {
        void vipOrdersQuery.refetch()
      },
    },
  )

  const columns = useMemo(() => {
    console.log('serviceQrCode:\n', serviceQrCode)
    return getTableColumns((item, type) => {
      if (type === 'cancel') {
        activeId.current = item.orderNo
        alertBaseManager.open({
          onSubmit: () => {
            statusMutation.mutate(activeId.current)
          },
          title: '确定取消订单吗',
        })
      }
      if (type === 'pay') {
        pay(item.orderNo, item.isUpgrade)
      }
      if (type === 'timeEnd') {
        void refetch()
      }
      if (type === 'detail') {
        activeId.current = item.orderNo
        setDetailOpen(true)
      }
    }, serviceQrCode || '')
  }, [serviceQrCode, statusMutation, pay, refetch])

  return (
    <div className="flex h-full flex-col">
      {/* 内容区域 */}
      <div className="flex-1 overflow-hidden">
        {vipOrdersQuery.isLoading && (
          <div className="flex flex-1 items-center justify-center">
            <Loading />
          </div>
        )}
        {vipOrdersQuery.isSuccess && (
          <StickyDataTable
            data={vipOrdersQuery.data.data}
            columns={columns}
            pagination={pagination}
            setPagination={setPagination}
            rowCount={vipOrdersQuery.data.totalSize}
            stickyColumns={{
              right: 1,
              left: 1,
            }}
          />
        )}
      </div>

      <OrderDetail id={activeId.current} open={detailOpen} setOpen={setDetailOpen} />
    </div>
  )
}
