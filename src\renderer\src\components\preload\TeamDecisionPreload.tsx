import type { ReactNode } from 'react'
import { useUserInfoPreload } from '@renderer/hooks/preload/user/useUserInfoPreload'
import { useTeamPreload } from '@renderer/hooks/preload/user/useTeamPreload'
import { LoadingScreen } from '@renderer/components/LoadingScreen'
import { Button } from '@renderer/shadcn-components/ui/button'
import { useLoginHandler } from '@renderer/hooks/preload/user/useLoginHandler'
import { useLogHandler } from '@renderer/hooks/preload/use-log-handler'
import { useTeamsStore } from '@renderer/store/teamsStore'
import { UserOnboarding } from '@renderer/pages/UserOnboarding'

/**
 * 团队决策预加载组件
 * 职责：
 * 1. 加载用户基础信息和团队信息
 * 2. 根据团队数量决定显示引导页面还是主应用
 * 3. 只有确定进入主应用时，才渲染 children（包含 UserContextPreload）
 */
export function TeamDecisionPreload({ children }: { children: ReactNode }) {
  const { ready: userInfoReady, error: userInfoError } = useUserInfoPreload()
  const { ready: teamsReady, error: teamsError } = useTeamPreload()
  const teams = useTeamsStore((state) => state.teams)

  useLoginHandler()
  useLogHandler()

  if (userInfoError || teamsError) {
    return (
      <div className="flex h-full w-full flex-col items-center justify-center">
        <span className="mb-2">获取用户数据失败</span>
        <Button
          onClick={() => {
            location.reload()
          }}
        >
          重试
        </Button>
      </div>
    )
  }

  // 如果数据还没加载完成，显示加载界面
  if (!userInfoReady || !teamsReady) {
    return <LoadingScreen tips="正在加载用户数据" />
  }

  // 如果用户没有团队，显示引导页面（不加载主应用的复杂组件）
  if (teams.length === 0) {
    return <UserOnboarding />
  }

  return children
}
