import type { AuthorizingAccountInfoStructure } from '@common/model'
import type { SpiderAccount, Account } from '../model'
import type {
  AppTaskPushingEventParam,
  ScriptUpdatedEventParam,
  VipUpgradeEventParam,
  OverseasAccountAuthorizedEventParam as OpenPlatformAccountAuthorizedEventParam,
} from '@renderer/hooks/preload/team/useSocketPreload'
import { Event } from '@renderer/infrastructure/event-bus/event'

export const systemEvents = {
  exitConfirming: new Event(Symbol()),
  deviceLogDownload: new Event<string>(Symbol()),
}

/**
 * @deprecated 这是较早期事件不多时用的统称，后续应该按业务划分，现存的事件逐步重构
 */
export const businessEvents = {
  scriptUpdated: new Event<ScriptUpdatedEventParam>(Symbol()),
}

export const advertiseEvents = {
  // 广告已更新
  advertiseUpdated: new Event(Symbol()),
}

export const notificationEvents = {
  // 有新消息
  newNotification: new Event(Symbol()),
  systemNotifyUpdated: new Event(Symbol()),
}

export const loginEvents = {
  // 登录成功
  loginOnAnotherDevice: new Event(Symbol()),
}

export const websocketEvents = {
  // 账号已更新
  accountUpdated: new Event<string>(Symbol()),
}

export const authorizeEvents = {
  // 账号已新增
  accountAdded: new Event<string>(Symbol()),
  // 账号已更新
  accountUpdated: new Event<Account>(Symbol()),
  // 账号已删除
  accountDeleted: new Event<string>(Symbol()),
  // 账号从浏览器授权成功
  accountAuthorizeSuccess: new Event<SpiderAccount>(Symbol()),

  // 开放平台账号授权完成
  openPlatformAccountAuthorized: new Event<OpenPlatformAccountAuthorizedEventParam>(Symbol()),

  cancelCollect: new Event<{ accountId: string; id: string }>(Symbol()),

  addCollect: new Event<{ accountId: string; url: string; name: string }>(Symbol()),

  webSpaceAuthorizeSuccess: new Event<{
    accountId: string
    url: string
    spaceName: string
    color: string
    unsaved: boolean
    cookies: Record<string, string>[]
    localStorage: Record<string, string>
  }>(Symbol()),

  accountAuthorizeSuccessV2: new Event<{
    accountId: string
    accountInfo: AuthorizingAccountInfoStructure
    cookies: Record<string, string>[]
    localStorage: Record<string, string>
    accountState: number
  }>(Symbol()),
}

export const pushEvents = {
  // 本地审核状态更新
  auditResultUpdated: new Event<string>(Symbol()),

  // app应用任务推送
  appTaskPushing: new Event<AppTaskPushingEventParam[]>(Symbol()),
}

export const teamEvents = {
  // 团队信息已改变
  teamDetailChanged: new Event<string>(Symbol()),
  // 团队版本升级
  teamVersionUpgrade: new Event<VipUpgradeEventParam>(Symbol()),
  // 团队版本降级
  teamVersionDowngrade: new Event(Symbol()),
  // 已被踢出团队
  beenRemoved: new Event<string>(Symbol()),
  // 成员信息已改变
  memberInfoChanged: new Event<string>(Symbol()),
  // 团队已加入
  teamJoined: new Event(Symbol()),

  // 成员已邀请
  memberInvited: new Event<string>(Symbol()),
  // 成员被移除
  memberRemoved: new Event<string>(Symbol()),
}

export const assertLibraryEvents = {
  capacityUpdated: new Event(Symbol()),
}

export const authenticationEvents = {
  serverError: new Event<string>(Symbol()),
  // 未授权
  unauthorized: new Event(Symbol()),
  businessError: new Event<string>(Symbol()),
  // 需vip权限
  needVip: new Event(Symbol()),
}

export const spaceEvents = {
  webSpaceDumpUpdated: new Event<string>(Symbol()),
  accountSpaceDumpUpdated: new Event<{ spaceId: string; accountId: string }>(Symbol()),
  FavoritesUpdated: new Event(Symbol()),

  // 空间图标已更新
  spaceIconUpdated: new Event<{
    spaceId: string
    icon: string | null
  }>(Symbol()),
}
