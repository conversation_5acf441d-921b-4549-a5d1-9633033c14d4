import { useNavigate, useSearchParams } from 'react-router-dom'
import { useNotify } from '@renderer/hooks/use-notify'
import { Card, CardContent, CardHeader } from '@renderer/shadcn-components/ui/card'
import { Button } from '@renderer/shadcn-components/ui/button'
import { Input } from '@renderer/shadcn-components/ui/input'
import { FormItem as LoginFormItem } from '@renderer/pages/Login/FormItem'
import PhoneIcon from '@renderer/assets/passport/phone.svg?react'
import VerifyIcon from '@renderer/assets/passport/verify.svg?react'
import { Loader2 } from 'lucide-react'
import { useGlobalStorageService } from '@renderer/infrastructure/services'
import { useWechatBindPhone } from '@renderer/hooks/useWechatLogin'
import { useForm, FormProvider } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { phoneRegex } from '@renderer/utils/zod'
import { VerificationCodeButton } from '@renderer/components/verificationCodeButton'

// 表单验证 schema
const linkAccountSchema = z.object({
  phone: z.string().regex(phoneRegex, '请输入正确的手机号'),
  code: z.string().min(6, '验证码为6位数字').max(6, '验证码为6位数字'),
})

type LinkAccountFormData = z.infer<typeof linkAccountSchema>

interface LinkAccountFormProps {
  code?: string
  state?: string
}

export function LinkAccountForm({ code, state }: LinkAccountFormProps) {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const { notifyService } = useNotify()
  const globalStorageService = useGlobalStorageService()

  // 如果没有直接传入code和state，则从URL中获取
  const wxCode = code || searchParams.get('code') || ''
  const wxState = state || searchParams.get('state') || ''

  // 初始化表单
  const form = useForm<LinkAccountFormData>({
    resolver: zodResolver(linkAccountSchema),
    defaultValues: {
      phone: '',
      code: '',
    },
  })

  // 关联微信账号
  const linkAccountMutation = useWechatBindPhone()

  // 确认绑定
  const handleConfirmLink = (data: LinkAccountFormData) => {
    if (!wxCode || !wxState) {
      notifyService.error('微信授权参数缺失')
      return
    }

    // 调用关联账号的API
    linkAccountMutation.mutate(
      {
        phone: data.phone,
        code: data.code,
        authorization: globalStorageService.getToken() || '',
      },
      {
        onSuccess: (result) => {
          notifyService.success('账号关联成功')
          globalStorageService.setToken(result.authorization)
          navigate('/')
        },
        onError: (error) => {
          notifyService.error(error.message || '账号关联失败')
        },
      },
    )
  }

  return (
    <FormProvider {...form}>
      <Card className="relative h-[534px] w-[500px] rounded-[20px] border-0 bg-white/95 shadow-xl backdrop-blur-sm">
        <CardHeader className="pb-6 pt-[120px]">
          <div className="text-center">
            <h2 className="text-2xl font-semibold text-gray-900">关联现有账号</h2>
          </div>
        </CardHeader>

        <CardContent className="space-y-6 p-[71px] pt-6">
          <form
            onSubmit={form.handleSubmit(handleConfirmLink)}
            className="grid w-full grid-cols-1 gap-4"
          >
            {/* 手机号输入 */}
            <LoginFormItem prepend={<PhoneIcon />}>
              <Input
                type="tel"
                {...form.register('phone')}
                className="h-[54px] bg-background/50 pl-10"
                placeholder="手机号码"
              />
            </LoginFormItem>

            {/* 验证码输入框 */}
            <LoginFormItem
              prepend={<VerifyIcon />}
              append={
                <VerificationCodeButton
                  sence="bindAccount"
                  phoneName="phone"
                  className="right-2 top-0 h-full"
                />
              }
            >
              <Input
                type="text"
                {...form.register('code')}
                className="h-[54px] bg-background/50 pl-10 pr-28"
                placeholder="验证码"
                maxLength={6}
              />
            </LoginFormItem>

            <div className="mt-8 flex justify-center space-x-4 pt-4">
              <Button
                type="submit"
                disabled={linkAccountMutation.isPending}
                className="h-12 w-full bg-primary text-white hover:bg-primary/90"
              >
                {linkAccountMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {linkAccountMutation.isPending ? '关联中...' : '确认绑定'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </FormProvider>
  )
}
