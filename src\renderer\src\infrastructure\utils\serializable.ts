import _ from 'lodash-es'

export function normalizeURL(url: string) {
  const urlObj = new URL(url)
  return urlObj.href
}

function isSerializable(obj: unknown) {
  if (
    _.isUndefined(obj) ||
    _.isNull(obj) ||
    _.isBoolean(obj) ||
    _.isNumber(obj) ||
    _.isString(obj)
  ) {
    return true
  }

  if (!_.isPlainObject(obj) && !_.isArray(obj)) {
    return false
  }

  const record = obj as Record<string, unknown>
  for (const key in record) {
    if (!isSerializable(record[key])) {
      return false
    }
  }

  return true
}

export function toSerializable<T>(obj: T) {
  //如果是proxy才替换
  if (!isSerializable(obj)) {
    return _.cloneDeep(obj)
  }
  return obj
}

export function toSerializableArgs(
  _target: unknown,
  _propertyKey: string,
  descriptor: PropertyDescriptor,
) {
  const originalMethod = descriptor.value

  descriptor.value = function (...args: []) {
    const serializableArgs = args.map((arg) => toSerializable(arg))
    return originalMethod.apply(this, serializableArgs)
  }

  return descriptor
}
