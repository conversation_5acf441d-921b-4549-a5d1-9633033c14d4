import { useUserApiService } from '@renderer/infrastructure/services/network-service'
import { WebSpace } from '@renderer/infrastructure/model/space/web-space'
import type {
  SpaceFavoriteResponse,
  SpaceResponse,
} from '@renderer/infrastructure/types/space-response'
import type { PagedResponse } from '@renderer/infrastructure/types'
import { PagedResponse2InfiniteQueryResponse } from '@renderer/infrastructure/types'
import { apiConverter } from '@renderer/infrastructure/services/network-service/api-converter'
import { useMemo } from 'react'
import type { ApiService } from '@renderer/infrastructure/services/network-service/api-service'
import type { FavoriteCache } from '@renderer/store/space-store'
import { useFavoriteCache } from '@renderer/store/space-store'
import type { SpaceFavorite } from '@renderer/infrastructure/model'
import type { AccountResponse } from '@renderer/infrastructure/types/account-response'
import type { AccountSession } from '@common/structure'
import { useCompressService } from '../../application-service/infrastructure-service/compress-service'
import { platformNames } from '@common/model/platform-name'

function uint8ArrayToBase64(array: Uint8Array): string {
  // 将 Uint8Array 转换为普通字符串
  const binaryString = Array.from(array)
    .map((byte) => String.fromCharCode(byte))
    .join('')

  // 使用 btoa 将二进制字符串转换为 base64
  return btoa(binaryString)
}

function base64ToUint8Array(base64: string): Uint8Array {
  // 使用 atob 将 base64 字符串转换为二进制字符串
  const binaryString = atob(base64)

  // 将二进制字符串转换为 Uint8Array
  const byteArray = new Uint8Array(binaryString.length)
  for (let i = 0; i < binaryString.length; i++) {
    byteArray[i] = binaryString.charCodeAt(i)
  }

  return byteArray
}

function Response2Favorite(favorite: SpaceFavoriteResponse) {
  return {
    id: favorite.id,
    name: favorite.name,
    url: favorite.websiteUrl,
  } satisfies SpaceFavorite as SpaceFavorite
}

export const Response2Space = (response: SpaceResponse): WebSpace => {
  try {
    return new WebSpace(
      response.id,
      response.spaceName,
      response.spaceUrl,
      response.color,
      response.isOperate,
      [],
      response.icon ?? null,
      response.updatedAt ? new Date(response.updatedAt) : null,
      response.favorites.map(Response2Favorite),
      response.checksum,
      response.isFreeze,
    )
  } catch (e) {
    console.error(e)
    throw new Error('SpaceResponse转换失败')
  }
}

export const AccountResponse2Space = (response: AccountResponse): WebSpace => {
  try {
    return new WebSpace(
      response.id,
      response.platformAccountName,
      response.spaceUrl ?? '',
      response.color!,
      response.isOperate,
      response.groups,
      null,
      response.updatedAt ? new Date(response.updatedAt) : null,
      response.favorites?.map(Response2Favorite) ?? [],
      response.checksum,
      response.isFreeze,
    )
  } catch (e) {
    console.error(e)
    throw new Error('SpaceResponse转换失败')
  }
}

export function useSpaceApi() {
  const userApiService = useUserApiService()
  const favoriteCache = useFavoriteCache()
  const compressService = useCompressService()
  return useMemo(() => {
    return new SpaceApi(userApiService, favoriteCache, compressService)
  }, [userApiService, favoriteCache, compressService])
}

export class SpaceApi {
  constructor(
    private userApiService: ApiService,
    private favoriteCache: FavoriteCache,
    private compressService: ReturnType<typeof useCompressService>,
  ) {}

  async createSpace(url: string, spaceName: string, color: string) {
    const spaceResponse = await this.userApiService.put<AccountResponse>('/platform-accounts', {
      spaceUrl: url,
      platformAccountName: spaceName,
      color: color,
      platformName: platformNames.Other,
    })
    return AccountResponse2Space(spaceResponse)
  }

  async updateWebSession(accountId: string, accountSession: AccountSession) {
    const cookies = this.compressService.deflateObject(accountSession.cookies)
    const localStorage = this.compressService.deflateObject(accountSession.localStorage)
    await this.userApiService.post(`/platform-accounts/${accountId}/cookie-refresh`, {
      cookie: uint8ArrayToBase64(cookies),
      localStorage: uint8ArrayToBase64(localStorage),
    })
  }

  async getWebSession(accountId: string): Promise<AccountSession> {
    const response = await this.userApiService.get<{
      cookie: string
      localStorage: string
    }>(`/platform-accounts/${accountId}/cookie`)
    try {
      const cookies = response.cookie ? base64ToUint8Array(response.cookie) : null
      const localStorage = response.localStorage ? base64ToUint8Array(response.localStorage) : null

      return {
        cookies: cookies ? this.compressService.inflateObject(cookies) : [],
        localStorage: localStorage ? this.compressService.inflateObject(localStorage) : {},
      } satisfies AccountSession
    } catch (e) {
      console.error(e)
      throw new Error('账号登录信息读取失败')
    }
  }

  async getSpaces(
    cursor: Date | null,
    options: { operableOnly: boolean; groupId?: string; spaceName?: string },
  ) {
    try {
      const spaceResponses = await this.userApiService.get<PagedResponse<SpaceResponse>>(
        '/site-spaces',
        {
          time: apiConverter.date2APINumber(cursor),
          isolation: options.operableOnly.toString(),
          groupId: options.groupId,
          spaceName: options.spaceName,
        },
      )

      const infiniteQueryResponse = PagedResponse2InfiniteQueryResponse(
        spaceResponses,
        cursor,
        Response2Space,
        (response) =>
          new Date(
            response.data.reduce((prev, current) =>
              prev.createdAt < current.createdAt ? prev : current,
            ).createdAt,
          ),
      )
      this.favoriteCache.updateSpaceFavorites(infiniteQueryResponse.data)
      return infiniteQueryResponse
    } catch (e) {
      console.error(e)
      throw new Error('获取空间失败')
    }
  }

  async getSpaceAll() {
    try {
      const spaceResponses = await this.userApiService.get<PagedResponse<SpaceResponse>>(
        '/site-spaces',
        {
          isolation: 'true',
          page: '1',
          size: '9999',
        },
      )

      const spaceData = spaceResponses.data.map((item) => Response2Space(item))

      this.favoriteCache.updateSpaceFavorites(spaceData)
      return spaceData
    } catch (e) {
      console.error(e)
      throw new Error('获取空间失败')
    }
  }

  async removeSpace(id: string) {
    return await this.userApiService.delete(`/site-spaces/${id}`)
  }

  async updateSpace(
    spaceId: string,
    {
      spaceName,
      color,
    }: {
      spaceName?: string
      color?: string
    } = {},
  ) {
    return await this.userApiService.patch<AccountResponse>(`/platform-accounts/${spaceId}`, {
      platformAccountName: spaceName,
      color: color,
    })
  }

  async getSpace(spaceId: string) {
    const spaceResponse = await this.userApiService.get<SpaceResponse>(`/site-spaces/${spaceId}`)
    const space = Response2Space(spaceResponse)
    this.favoriteCache.updateSpaceFavorites([space])
    return space
  }

  async saveFavorite(accountId: string, name: string, url: string) {
    const response = await this.userApiService.put<SpaceFavoriteResponse>(
      `/platform-accounts/${accountId}/collect`,
      {
        name: name,
        websiteUrl: url,
      },
    )
    this.favoriteCache.updateFavorite(accountId, Response2Favorite(response))
  }

  async cancelCollect(accountId: string, favoriteId: string) {
    return this.userApiService.delete(`/platform-accounts/${accountId}/collect/${favoriteId}`)
  }

  async removeFavorite(accountId: string, url: string) {
    const favorites = this.favoriteCache.spaceFavorites.get(accountId)
    if (favorites) {
      const favoriteId = favorites.find((favorite) => favorite.url === url)?.id
      if (favoriteId) {
        await this.userApiService.delete(`/platform-accounts/${accountId}/collect/${favoriteId}`)
      }
    }
    this.favoriteCache.removeFavorite(accountId, url)
  }
}
