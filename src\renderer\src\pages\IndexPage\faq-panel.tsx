import { ScrollArea } from '@renderer/shadcn-components/ui/scroll-area'
import { OverflowTooltip } from '@renderer/components/OverflowTooltip'
import { useQuery } from '@tanstack/react-query'
import { useUserApiService } from '@renderer/infrastructure/services/network-service'
import { useCallback, useMemo } from 'react'

/**
 * FAQ问题详情
 */
export interface QuestionDetail {
  /**
   * 问题id
   */
  id: string
  /**
   * 跳转地址
   */
  questionUrl: string
  /**
   * 排序
   */
  sort: number
  /**
   * 标题
   */
  title: string
}

/**
 * FAQ列表响应数据
 */
export interface QuestionListResponse {
  data: QuestionDetail[]
  page: number
  size: number
  totalPage: number
  totalSize: number
}

/**
 * FAQ对象，用于UI展示
 */
export class FAQ {
  constructor(
    public title: string,
    public link: string,
    public id: string,
    public sort: number,
  ) {}

  /**
   * 从API响应数据创建FAQ对象
   */
  static fromQuestionDetail(detail: QuestionDetail): FAQ {
    return new FAQ(detail.title, detail.questionUrl, detail.id, detail.sort)
  }
}

/**
 * FAQ API服务hook
 */
function useFAQApi() {
  const userApiService = useUserApiService()

  const getQuestions = useCallback(
    async (page = 1, size = 20): Promise<QuestionListResponse> => {
      return userApiService.get<QuestionListResponse>('/question', {
        page: page.toString(),
        size: size.toString(),
      })
    },
    [userApiService],
  )

  return useMemo(
    () => ({
      getQuestions,
    }),
    [getQuestions],
  )
}

/**
 * FAQ查询hook
 */
function useFAQQuery() {
  const { getQuestions } = useFAQApi()

  return useQuery({
    queryKey: ['faq-questions'],
    queryFn: async () => {
      const response = await getQuestions(1, 100) // 获取前100个FAQ
      // 按sort字段排序，然后转换为FAQ对象
      return response.data
        .sort((a, b) => a.sort - b.sort)
        .map((detail) => FAQ.fromQuestionDetail(detail))
    },
    initialData: [],
    staleTime: 5 * 60 * 1000, // 5分钟内不重新请求
  })
}

export function FAQList() {
  const { data: faqs, isLoading, error } = useFAQQuery()

  if (isLoading) {
    return (
      <div className="flex h-full grow flex-col px-2">
        <div className="flex items-center justify-center py-8">
          <div className="text-sm text-muted-foreground">加载中...</div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex h-full grow flex-col px-2">
        <div className="flex items-center justify-center py-8">
          <div className="text-sm text-muted-foreground">加载失败</div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-full grow flex-col px-2">
      {faqs.length > 0 && (
        <div>
          {faqs.map((faq) => (
            <div
              key={faq.id}
              className="group flex h-9 cursor-pointer items-center rounded-md py-1.5 transition duration-300 hover:bg-accent"
              onClick={() => {
                window.open(faq.link)
              }}
            >
              <div className="mx-2 flex h-[8px] w-[8px] items-center justify-center">
                <div className="h-[5px] w-[5px] rounded-full bg-primary"></div>
              </div>
              <OverflowTooltip className="text-sm" tooltip={faq.title}>
                {faq.title}
              </OverflowTooltip>
            </div>
          ))}
        </div>
      )}
      {faqs.length === 0 && (
        <div className="flex items-center justify-center py-8">
          <div className="text-sm text-muted-foreground">暂无常见问题</div>
        </div>
      )}
    </div>
  )
}

export function FAQPanel() {
  return (
    <div className="relative flex max-h-[312px] flex-col rounded-lg bg-white">
      <div className="mx-4 mb-2 mt-4">
        <h3 className="font-semibold">常见问题</h3>
      </div>
      <ScrollArea className="max-h-[260px] min-h-[200px] overflow-y-auto">
        <FAQList />
      </ScrollArea>
    </div>
  )
}
