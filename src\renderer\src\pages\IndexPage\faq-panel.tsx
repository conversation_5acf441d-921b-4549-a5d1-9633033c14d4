import { ScrollArea } from '@renderer/shadcn-components/ui/scroll-area'
import { OverflowTooltip } from '@renderer/components/OverflowTooltip'

export class FAQ {
  constructor(
    public title: string,
    public link: string,
  ) {}
}

const faqs = [
  new FAQ(
    '账号为什么会授权失败、添加失败？',
    'https://yixiaoer.feishu.cn/wiki/S2vywDk49iyNJlkhEVAc41sGn6g#share-EqVtdiGoRoGiCvxRUbXctZYQnRh',
  ),
  new FAQ(
    '支持手机端用吗，手机要怎么发？',
    'https://yixiaoer.feishu.cn/wiki/S2vywDk49iyNJlkhEVAc41sGn6g#share-IePjdvPWpoxZWbx9HiqcSVkenCf',
  ),
  new FAQ(
    '发布视频怎么挂小黄车链接，小程序等？',
    'https://yixiaoer.feishu.cn/wiki/S2vywDk49iyNJlkhEVAc41sGn6g#share-WNhXdA77eoBuMcxXF1bch32Rned',
  ),
  new FAQ(
    '其他账号要怎么使用，例如登录淘宝光合？',
    'https://yixiaoer.feishu.cn/wiki/S2vywDk49iyNJlkhEVAc41sGn6g#share-PX7BdGRkAoD27nxkGnic8p3ln1g',
  ),
  new FAQ(
    '为什么会登录失效？',
    'https://yixiaoer.feishu.cn/wiki/S2vywDk49iyNJlkhEVAc41sGn6g#share-DQ5LdRgw3odbECxWQ81c8XX8nQd',
  ),
  new FAQ(
    '从哪里进入官方<创作者中心>？',
    'https://yixiaoer.feishu.cn/wiki/S2vywDk49iyNJlkhEVAc41sGn6g#share-QLxCdcC4houYdkxWxHNc1g2jnVc',
  ),
]

export function FAQList() {
  return (
    <div className="flex h-full grow flex-col px-2">
      {faqs.length > 0 && (
        <div>
          {faqs.map((faq) => (
            <div
              key={faq.title}
              className="group flex h-9 cursor-pointer items-center rounded-md py-1.5 transition duration-300 hover:bg-accent"
              onClick={() => {
                window.open(faq.link)
              }}
            >
              <div className="mx-2 flex h-[8px] w-[8px] items-center justify-center">
                <div className="h-[5px] w-[5px] rounded-full bg-primary"></div>
              </div>
              <OverflowTooltip className="text-sm" tooltip={faq.title}>
                {faq.title}
              </OverflowTooltip>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export function FAQPanel() {
  return (
    <div className="relative flex max-h-[312px] flex-col rounded-lg bg-white">
      <div className="mx-4 mb-2 mt-4">
        <h3 className="font-semibold">常见问题</h3>
      </div>
      <ScrollArea className="max-h-[260px] min-h-[200px] overflow-y-auto">
        <FAQList />
      </ScrollArea>
    </div>
  )
}
